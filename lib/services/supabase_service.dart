import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/product.dart';
import '../models/ingredient.dart';
import '../models/user.dart' as AppUser;
import 'supabase_init_service.dart';
import 'logger_service.dart';

class SupabaseService {
  final SupabaseClient client;
  SupabaseService(this.client);

  // Auth methods
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? name,
  }) async {
    return await client.auth.signUp(
      email: email,
      password: password,
      data: name != null ? {'name': name} : null,
    );
  }

  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await client.auth.signOut();
  }

  AppUser.User? get currentUser {
    final user = client.auth.currentUser;
    if (user == null) return null;
    return AppUser.User.fromJson(user.toJson());
  }

  Stream<AuthState> get authStateChanges {
    return client.auth.onAuthStateChange;
  }

  // Product methods
  Future<List<Product>> getUserProducts() async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await client
        .from('products')
        .select('*')
        .eq('user_id', user.id)
        .order('scanned_at', ascending: false);

    return (response as List)
        .map((json) => Product.fromJson(json))
        .toList();
  }

  Future<Product?> getProductByBarcode(String barcode) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await client
        .from('products')
        .select('*')
        .eq('barcode', barcode)
        .eq('user_id', user.id)
        .maybeSingle();

    if (response == null) return null;
    return Product.fromJson(response);
  }

  Future<List<Product>> searchProducts(String query) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await client
        .from('products')
        .select('*')
        .eq('user_id', user.id)
        .or('name.ilike.%$query%,brand.ilike.%$query%')
        .order('scanned_at', ascending: false);

    return (response as List)
        .map((json) => Product.fromJson(json))
        .toList();
  }

  // Ingredient methods
  Future<List<Ingredient>> searchIngredients(String query) async {
    final response = await client
        .from('ingredients')
        .select('*')
        .or('name.ilike.%$query%,common_aliases.cs.{$query}')
        .limit(50);

    return (response as List)
        .map((json) => Ingredient.fromJson(json))
        .toList();
  }

  Future<Ingredient> addIngredient(AnalyzedIngredient analyzedIngredient) async {
    final ingredientData = {
      'name': analyzedIngredient.name,
      'description': analyzedIngredient.description,
      'safety_level': analyzedIngredient.safetyLevel,
      'health_risks': analyzedIngredient.healthRisks,
      'common_aliases': analyzedIngredient.aliases,
      'category': analyzedIngredient.category,
    };

    final response = await client
        .from('ingredients')
        .insert(ingredientData)
        .select()
        .single();

    return Ingredient.fromJson(response);
  }

  Future<List<Ingredient>> getAllIngredients() async {
    final response = await client
        .from('ingredients')
        .select('*')
        .order('name');

    return (response as List)
        .map((json) => Ingredient.fromJson(json))
        .toList();
  }

  // Saved products methods
  Future<void> saveProductForUser(String productId) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    await client.from('saved_products').insert({
      'user_id': user.id,
      'product_id': productId,
    });
  }

  Future<void> unsaveProductForUser(String productId) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    await client
        .from('saved_products')
        .delete()
        .eq('user_id', user.id)
        .eq('product_id', productId);
  }

  Future<List<Product>> getSavedProducts() async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await client
        .from('saved_products')
        .select('products(*)')
        .eq('user_id', user.id)
        .order('saved_at', ascending: false);

    return (response as List)
        .map((item) => Product.fromJson(item['products']))
        .toList();
  }

  Future<bool> isProductSaved(String productId) async {
    final user = client.auth.currentUser;
    if (user == null) return false;

    final response = await client
        .from('saved_products')
        .select('id')
        .eq('user_id', user.id)
        .eq('product_id', productId)
        .maybeSingle();

    return response != null;
  }

  // User preferences methods
  Future<AppUser.UserPreferences?> getUserPreferences() async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await client
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

    if (response == null) return null;
    return AppUser.UserPreferences.fromJson(response);
  }

  Future<AppUser.UserPreferences> saveUserPreferences(AppUser.UserPreferences preferences) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final preferencesData = preferences.toJson();
    preferencesData['user_id'] = user.id;

    final response = await client
        .from('user_preferences')
        .upsert(preferencesData)
        .select()
        .single();

    return AppUser.UserPreferences.fromJson(response);
  }

  // Analytics methods
  Future<Map<String, int>> getUserStats() async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    // Get total scans
    final totalScansResponse = await client
        .from('products')
        .select('id')
        .eq('user_id', user.id)
        .count();

    // Get safe products count
    final safeProductsResponse = await client
        .from('products')
        .select('id')
        .eq('user_id', user.id)
        .gte('safety_score', 80)
        .count();

    // Get saved products count
    final savedProductsResponse = await client
        .from('saved_products')
        .select('id')
        .eq('user_id', user.id)
        .count();

    return {
      'totalScans': totalScansResponse.count ?? 0,
      'safeProducts': safeProductsResponse.count ?? 0,
      'savedProducts': savedProductsResponse.count ?? 0,
    };
  }

  Future<List<Map<String, dynamic>>> getScanHistory(int days) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final startDate = DateTime.now().subtract(Duration(days: days));

    final response = await client
        .from('products')
        .select('scanned_at, safety_score')
        .eq('user_id', user.id)
        .gte('scanned_at', startDate.toIso8601String())
        .order('scanned_at');

    return List<Map<String, dynamic>>.from(response);
  }

  // Allergens methods
  Future<List<AppUser.Allergen>> getAllergens() async {
    final response = await client
        .from('allergens')
        .select('*')
        .order('name');

    return (response as List)
        .map((json) => AppUser.Allergen.fromJson(json))
        .toList();
  }

  // Enhanced AI and caching methods
  Future<void> cacheAIAnalysis(String productId, Map<String, dynamic> analysis) async {
    try {
      await client
          .from('ai_analysis_cache')
          .upsert({
            'product_id': productId,
            'analysis_data': analysis,
            'created_at': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      LoggerService.error('Error caching AI analysis: $e');
    }
  }

  Future<Map<String, dynamic>?> getCachedAIAnalysis(String productId) async {
    try {
      final response = await client
          .from('ai_analysis_cache')
          .select()
          .eq('product_id', productId)
          .gte('created_at', DateTime.now().subtract(const Duration(days: 7)).toIso8601String())
          .single();
      
      return response['analysis_data'] as Map<String, dynamic>?;
    } catch (e) {
      // No cached analysis found or error
      return null;
    }
  }

  Future<void> storeIngredientData(String name, Map<String, dynamic> data) async {
    try {
      await client
          .from('ingredients')
          .upsert({
            'name': name.toLowerCase().trim(),
            'description': data['description'],
            'safety_level': data['safety_level'] ?? 3, // Use int instead of text
            'health_risks': data['health_risks'],
            'common_aliases': data['common_aliases'],
            'updated_at': DateTime.now().toIso8601String(),
          });
      
      LoggerService.info('Ingredient data stored: $name');
    } catch (e) {
      LoggerService.error('Error storing ingredient data for $name: $e');
    }
  }

  Future<Map<String, dynamic>?> getIngredientData(String name) async {
    try {
      final response = await client
          .from('ingredients')
          .select()
          .eq('name', name.toLowerCase())
          .single();
      
      return response;
    } catch (e) {
      return null;
    }
  }

  Future<void> storeUserFeedback({
    required String userId,
    required String productId,
    required String feedbackType,
    required Map<String, dynamic> feedbackData,
  }) async {
    try {
      await client
          .from('user_feedback')
          .insert({
            'user_id': userId,
            'product_id': productId,
            'feedback_type': feedbackType, // 'safety_rating', 'ingredient_accuracy', 'recommendation_quality'
            'feedback_data': feedbackData,
            'created_at': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      LoggerService.error('Error storing user feedback: $e');
    }
  }

  Future<Map<String, dynamic>?> getEnhancedUserPreferences(String userId) async {
    try {
      final response = await client
          .from('user_preferences')
          .select()
          .eq('user_id', userId)
          .single();
      
      return response;
    } catch (e) {
      LoggerService.error('Error getting user preferences: $e');
      return null;
    }
  }

  Future<void> updateEnhancedUserPreferences(String userId, Map<String, dynamic> preferences) async {
    try {
      await client
          .from('user_preferences')
          .upsert({
            'user_id': userId,
            'allergens': preferences['allergens'],
            'avoided_ingredients': preferences['avoided_ingredients'],
            'dietary_restrictions': preferences['dietary_restrictions'],
            'health_conditions': preferences['health_conditions'],
            'preferred_brands': preferences['preferred_brands'],
            'safety_threshold': preferences['safety_threshold'],
            'updated_at': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      LoggerService.error('Error updating user preferences: $e');
      rethrow;
    }
  }

  Future<void> storeProductRating({
    required String productId,
    required int rating,
    required int safetyRating,
    String? review,
    Map<String, dynamic>? additionalData,
  }) async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    try {
      await client
          .from('product_ratings')
          .upsert({
            'user_id': user.id,
            'product_id': productId,
            'rating': rating,
            'safety_rating': safetyRating,
            'review': review,
            'additional_data': additionalData,
            'created_at': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      LoggerService.error('Error storing product rating: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getProductRatings(String productId) async {
    try {
      final response = await client
          .from('product_ratings')
          .select('*, users(name)')
          .eq('product_id', productId)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      LoggerService.error('Error getting product ratings: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> getAIPerformanceMetrics() async {
    final user = client.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    try {
      // Get accuracy feedback
      final accuracyResponse = await client
          .from('user_feedback')
          .select('feedback_data')
          .eq('user_id', user.id)
          .eq('feedback_type', 'ai_accuracy');

      // Get usage statistics
      final usageResponse = await client
          .from('ai_analysis_cache')
          .select('created_at')
          .gte('created_at', DateTime.now().subtract(const Duration(days: 30)).toIso8601String())
          .count();

      return {
        'accuracy_feedback': accuracyResponse,
        'monthly_usage': usageResponse.count ?? 0,
      };
    } catch (e) {
      LoggerService.error('Error getting AI performance metrics: $e');
      return {};
    }
  }

  Future<void> logScanEvent(
    String barcode, 
    String requestId, 
    DateTime timestamp, 
    String? userId,
  ) async {
    try {
      await client
          .from('scan_events')
          .insert({
            'barcode': barcode,
            'request_id': requestId,
            'timestamp': timestamp.toIso8601String(),
            'user_id': userId,
          });
    } catch (e) {
      LoggerService.error('Error logging scan event: $e');
    }
  }

  Future<void> storeUnknownBarcode(String barcode, String? userId) async {
    try {
      // First check if this barcode is already in the unknown list
      final existing = await client
          .from('unknown_barcodes')
          .select('id')
          .eq('barcode', barcode)
          .maybeSingle();
      
      if (existing != null) {
        // Update counter if exists, with null check
        final currentCount = existing['count'] ?? 0;
        await client
            .from('unknown_barcodes')
            .update({
              'count': (currentCount is int ? currentCount : int.tryParse(currentCount.toString()) ?? 0) + 1,
              'last_scan': DateTime.now().toIso8601String(),
            })
            .eq('id', existing['id']);
      } else {
        // Insert new entry if not exists
        await client.from('unknown_barcodes').insert({
          'barcode': barcode,
          'count': 1,
          'first_scan': DateTime.now().toIso8601String(),
          'last_scan': DateTime.now().toIso8601String(),
          'user_id': userId ?? 'anonymous',
        });
      }
    } catch (e) {
      // Non-critical: log the error but continue execution
      LoggerService.error('Error storing unknown barcode: $e');
      
      // Try storing in fallback table
      try {
        await client.from('fallback_unknown_barcodes').insert({
          'barcode': barcode,
          'scan_time': DateTime.now().toIso8601String(),
          'user_id': userId ?? 'anonymous',
        });
      } catch (fallbackError) {
        // Handle 404 errors gracefully - table may not exist
        if (fallbackError.toString().contains('404')) {
          LoggerService.info('Fallback unknown barcodes table not available: $fallbackError');
        } else {
          LoggerService.error('Error logging to fallback table: $fallbackError');
        }
      }
    }
  }

  Future<Ingredient?> findIngredientByName(String name) async {
    try {
      final normalizedName = name.toLowerCase().trim();
      
      // Try direct match first
      final directResponse = await client
          .from('ingredients')
          .select('*')
          .eq('name', normalizedName)
          .maybeSingle();
          
      if (directResponse != null) {
        return Ingredient.fromJson(directResponse);
      }
      
      // Try alias match
      final aliasResponse = await client
          .from('ingredients')
          .select('*')
          .filter('common_aliases', 'cs', '{$normalizedName}')
          .maybeSingle();
          
      if (aliasResponse != null) {
        return Ingredient.fromJson(aliasResponse);
      }
      
      // No match found
      return null;
    } catch (e) {
      LoggerService.error('Error finding ingredient by name: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> findIngredient(String normalizedName) async {
    try {
      LoggerService.info('Looking up ingredient: $normalizedName', context: 'findIngredient');
      
      // First try exact match
      var response = await client
          .from('ingredients')
          .select('name, description, safety_level, health_risks, common_names')
          .eq('name', normalizedName)
          .limit(1);
      
      if (response.isNotEmpty) {
        final ingredient = response.first;
        LoggerService.info('Found exact match for ingredient: $normalizedName', context: 'findIngredient');
        return _formatIngredientResult(ingredient);
      }
      
      // Try case-insensitive search
      response = await client
          .from('ingredients')
          .select('name, description, safety_level, health_risks, common_names')
          .ilike('name', '%$normalizedName%')
          .limit(1);
      
      if (response.isNotEmpty) {
        final ingredient = response.first;
        LoggerService.info('Found partial match for ingredient: $normalizedName', context: 'findIngredient');
        return _formatIngredientResult(ingredient);
      }
      
      // Try searching in common_names field
      response = await client
          .from('ingredients')
          .select('name, description, safety_level, health_risks, common_names')
          .ilike('common_names', '%$normalizedName%')
          .limit(1);
      
      if (response.isNotEmpty) {
        final ingredient = response.first;
        LoggerService.info('Found ingredient via common names: $normalizedName', context: 'findIngredient');
        return _formatIngredientResult(ingredient);
      }
      
      // Also check ingredients_lookup table if available
      try {
        response = await client
            .from('ingredients_lookup')
            .select('name, description, safety_level, health_risks')
            .ilike('name', '%$normalizedName%')
            .limit(1);
        
        if (response.isNotEmpty) {
          final ingredient = response.first;
          LoggerService.info('Found ingredient in lookup table: $normalizedName', context: 'findIngredient');
          return _formatIngredientResult(ingredient);
        }
      } catch (e) {
        // ingredients_lookup table might not exist, continue
        LoggerService.warning('ingredients_lookup table not available: $e', context: 'findIngredient');
      }
      
      LoggerService.info('No match found for ingredient: $normalizedName', context: 'findIngredient');
      return null;
      
    } catch (e) {
      LoggerService.error('Error finding ingredient $normalizedName: $e', context: 'findIngredient');
      return null;
    }
  }

  Map<String, dynamic> _formatIngredientResult(Map<String, dynamic> ingredient) {
    return {
      'name': ingredient['name'],
      'description': ingredient['description'],
      'safety_level': ingredient['safety_level'],
      'health_risks': ingredient['health_risks'],
      'common_names': ingredient['common_names'],
    };
  }

  Future<Product> saveProduct(Product product) async {
    try {
      LoggerService.info('[saveProduct] Saving product to database');
      
      // Use product_analysis_fullscan table
      final Map<String, dynamic> productData = {
        'barcode': product.barcode,
        'name': product.name.isNotEmpty ? product.name : 'Unknown Product',
        'safety_score': product.safetyScore ?? 50,
        'safety_rating': product.safetyRating ?? 'unknown',
        'scanned_at': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'concerns_count': product.concernsCount ?? 0,
      };
      
      // Add optional fields
      if (product.brand != null && product.brand!.isNotEmpty) {
        productData['brand'] = product.brand;
      }
      
      if (product.imageUrl != null && product.imageUrl!.isNotEmpty) {
        productData['image'] = product.imageUrl;
      }

      // Add ingredients as JSON
      if (product.ingredients.isNotEmpty) {
        productData['ingredients_json'] = product.ingredients.map((i) => i.toJson()).toList();
      }

      // Add alternatives as JSON
      if (product.alternatives.isNotEmpty) {
        productData['alternatives_json'] = product.alternatives.map((a) => a.toJson()).toList();
      }
      
      // Add user_id if authenticated
      final user = client.auth.currentUser;
      if (user != null) {
        productData['user_id'] = user.id;
      }
      
      // Insert into product_analysis_fullscan
      final response = await client
          .from('product_analysis_fullscan')
          .insert(productData)
          .select()
          .single();
      
      LoggerService.info('[saveProduct] Product saved successfully');
      return Product.fromDatabaseJson(Map<String, dynamic>.from(response));
      
    } catch (e) {
      LoggerService.error('[saveProduct] Error: $e');
      rethrow;
    }
  }
  
  String _generateProductId() {
    return 'product_${DateTime.now().millisecondsSinceEpoch}_${(1000 + (999 * 0.5)).round()}';
  }

  Future<void> addNewIngredient({
    required String name,
    String? category,
    String? description,
    required String safetyLevel,
    List<String> healthRisks = const [],
    List<String> commonAliases = const [],
  }) async {
    try {
      final normalizedName = name.toLowerCase().trim();
      // Check if ingredient already exists
      final existingIngredient = await findIngredientByName(normalizedName);
      if (existingIngredient != null) {
        LoggerService.info('Ingredient already exists: $normalizedName');
        return;
      }
      await client
          .from('pending_ingredients')
          .insert({
            'name': name,
            'normalized_name': normalizedName,
            'category': category,
            'description': description,
            'safety_level': safetyLevel,
            'health_risks': healthRisks,
            'common_aliases': commonAliases,
            'pending_review': true,
            'ai_generated': true,
            'created_at': DateTime.now().toIso8601String(),
          });
      LoggerService.info('Added new ingredient to pending review: $name');
    } catch (e) {
      LoggerService.error('Error adding new ingredient: $e');
    }
  }

  Future<void> storeNewIngredients(List<Map<String, dynamic>> newIngredients) async {
    try {
      if (newIngredients.isEmpty) return;
      await client
          .from('pending_ingredients')
          .insert(
            newIngredients.map((ingredient) => {
              'name': ingredient['name'],
              'normalized_name': ingredient['normalized_name'],
              'safety_level': ingredient['safety_level'],
              'description': ingredient['description'],
              'health_risks': ingredient['health_risks'],
              'pending_review': true,
              'ai_generated': true,
              'request_id': ingredient['request_id'],
              'created_at': DateTime.now().toIso8601String(),
            }).toList()
          );
    } catch (e) {
      LoggerService.error('Error storing new ingredients: $e');
    }
  }

  Future<void> saveProductForUser(String productId, String userId) async {
    try {
      await client.from('saved_products').insert({
        'user_id': userId,
        'product_id': productId, // This should match the UUID from product_analysis_fullscan
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      LoggerService.info('Product saved for user: $productId');
    } catch (e) {
      LoggerService.error('Error saving product for user: $e');
    }
  }

  Future<List<Product>> getUserSavedProducts(String userId) async {
    try {
      final response = await client
          .from('saved_products')
          .select('product_id')
          .eq('user_id', userId);
      
      final productIds = response.map((item) => item['product_id'] as String).toList();
      
      if (productIds.isEmpty) return [];
      
      final productsResponse = await client
          .from('product_analysis_fullscan')
          .select('*')
          .in_('id', productIds);
      
      return productsResponse
          .map((json) => Product.fromDatabaseJson(Map<String, dynamic>.from(json)))
          .toList();
    } catch (e) {
      LoggerService.error('Error getting saved products: $e');
      return [];
    }
  }
}
