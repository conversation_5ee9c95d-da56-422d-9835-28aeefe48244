import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/product.dart';
import '../services/supabase_service.dart';
import '../services/enhanced_safescan_service.dart';
import 'auth_provider.dart';
import 'supabase_provider.dart';

// Enhanced SafeScan service provider (implements full Mermaid flow)
final enhancedSafeScanServiceProvider = Provider<EnhancedSafeScanService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return EnhancedSafeScanService(supabaseService);
});

// Products list provider
final userProductsProvider = FutureProvider<List<Product>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];
  final supabaseService = ref.watch(supabaseServiceProvider);
  return await supabaseService.getUserProducts();
});

// Saved products provider
final savedProductsProvider = FutureProvider<List<Product>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];
  final supabaseService = ref.watch(supabaseServiceProvider);
  return await supabaseService.getSavedProducts();
});

// User stats provider
final userStatsProvider = FutureProvider<Map<String, int>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return {'totalScans': 0, 'safeProducts': 0, 'savedProducts': 0};
  final supabaseService = ref.watch(supabaseServiceProvider);
  return await supabaseService.getUserStats();
});

// Scan history provider
final scanHistoryProvider = FutureProvider.family<List<Map<String, dynamic>>, int>((ref, days) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];
  final supabaseService = ref.watch(supabaseServiceProvider);
  return await supabaseService.getScanHistory(days);
});

// Product search provider
final productSearchProvider = FutureProvider.family<List<Product>, String>((ref, query) async {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return await supabaseService.searchProducts(query);
});

// Product controller (now using EnhancedProductAnalysisService)
class ProductController extends StateNotifier<AsyncValue<Product?>> {
  ProductController(this.ref) : super(const AsyncValue.data(null));
  final Ref ref;

  void setCurrentProduct(Product product) {
    state = AsyncValue.data(product);
  }

  Future<void> scanProductByBarcode(String barcode, {String? imagePath}) async {
    state = const AsyncValue.loading();
    try {
      final userId = ref.read(currentUserProvider)?.id;
      
      // Use Enhanced SafeScan Service for complete flow
      final product = await ref.read(enhancedSafeScanServiceProvider).analyzeProductByBarcode(
        barcode,
        userId: userId,
        imagePath: imagePath,
      );

      if (product != null) {
        state = AsyncValue.data(product);
      } else {
        state = AsyncValue.error('Product analysis failed', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Manual product analysis
  Future<void> analyzeManualProduct(String productName, String brand) async {
    if (state is AsyncLoading) return;
    
    state = const AsyncValue.loading();
    
    try {
      print('[analyzeManualProduct] Starting analysis for: $productName');
      print('[analyzeManualProduct] Brand: $brand');
      
      // TODO: Re-implement when EnhancedSafeScanService has analyzeManualProduct method
      // For now, create a basic product
      final product = Product(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: productName,
        brand: brand,
        barcode: '',
        safetyScore: 50, // Default score
        ingredients: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        scannedAt: DateTime.now(),
        safetyRating: 'medium', // Default rating
      );
      
      print('[analyzeManualProduct] Created basic product: ${product.name}');
      
      // Update state with the new product
      state = AsyncValue.data(product);
      
      // Refresh related providers
      ref.invalidate(userProductsProvider);
      ref.invalidate(userStatsProvider);
      
      print('[analyzeManualProduct] Analysis completed successfully');
      
    } catch (e) {
      print('[analyzeManualProduct] Error during analysis: $e');
      state = AsyncValue.error('Failed to analyze product: $e', StackTrace.current);
    }
  }

  Future<void> saveProduct(Product product) async {
    state = const AsyncValue.loading();
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final savedProduct = await supabaseService.saveProduct(product);
      state = AsyncValue.data(savedProduct);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  void clearCurrentProduct() {
    state = const AsyncValue.data(null);
  }
}

// Custom exception for product analysis
class ProductAnalysisException implements Exception {
  final String code;
  final String message;
  
  ProductAnalysisException(this.code, this.message);
  
  @override
  String toString() => 'ProductAnalysisException($code): $message';
}

// Product controller provider
final productControllerProvider = StateNotifierProvider<ProductController, AsyncValue<Product?>>((ref) {
  return ProductController(ref);
});

// Saved products controller
class SavedProductsController extends StateNotifier<AsyncValue<void>> {
  SavedProductsController(this.ref) : super(const AsyncValue.data(null));
  final Ref ref;

  Future<void> saveProduct(String productId) async {
    state = const AsyncValue.loading();
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      await supabaseService.saveProductForUser(productId);
      state = const AsyncValue.data(null);
      // Refresh saved products list
      ref.invalidate(savedProductsProvider);
      ref.invalidate(userStatsProvider);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> removeProduct(String productId) async {
    state = const AsyncValue.loading();
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      await supabaseService.unsaveProductForUser(productId);
      state = const AsyncValue.data(null);
      // Refresh saved products list
      ref.invalidate(savedProductsProvider);
      ref.invalidate(userStatsProvider);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<bool> isProductSaved(String productId) async {
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      return await supabaseService.isProductSaved(productId);
    } catch (e) {
      return false;
    }
  }
}

// Saved products controller provider
final savedProductsControllerProvider = StateNotifierProvider<SavedProductsController, AsyncValue<void>>((ref) {
  return SavedProductsController(ref);
});

// Product by ID provider
final productByIdProvider = FutureProvider.family<Product?, String>((ref, productId) async {
  final products = await ref.watch(userProductsProvider.future);
  try {
    return products.firstWhere((product) => product.id == productId);
  } catch (e) {
    return null;
  }
});

// Recent products provider (last 5)
final recentProductsProvider = Provider<AsyncValue<List<Product>>>((ref) {
  final productsAsync = ref.watch(userProductsProvider);
  return productsAsync.when(
    data: (products) => AsyncValue.data(products.take(5).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Product alternatives provider (enhanced with Gemini recommendations)
final productAlternativesProvider = FutureProvider.family<List<Map<String, dynamic>>, Product>((ref, product) async {
  // Use the Enhanced SafeScan Service to get better alternatives through enhanced analysis
  final analysisService = ref.read(enhancedSafeScanServiceProvider);
  
  try {
    // TODO: Re-implement when EnhancedSafeScanService has getProductAlternatives method
    // For now, skip enhanced alternatives and go straight to fallback
    print('[ProductAlternatives] Getting alternatives for: ${product.name}');
    
    // Original code (commented out until method exists):
    // final alternatives = await analysisService.getProductAlternatives(product);
    // if (alternatives.isNotEmpty) {
    //   return alternatives;
    // }
    
  } catch (e) {
    print('[ProductAlternatives] Error getting enhanced alternatives: $e');
  }
  
  // Fallback to simple alternatives
  if ((product.safetyScore ?? 0) >= 70) {
    return [
      {
        'name': 'Similar Safe Product',
        'brand': 'Organic Brand',
        'safety_score': 90,
        'recommendation': 'good',
        'why_better': 'Contains similar safe ingredients',
        'availability': 'common',
      },
    ];
  } else {
    return [
      {
        'name': 'Organic Alternative',
        'brand': 'Natural Brand',
        'safety_score': 95,
        'recommendation': 'good',
        'why_better': 'Contains only organic ingredients with no concerning additives',
        'availability': 'common',
      },
      {
        'name': 'Clean Label Option',
        'brand': 'Pure Foods',
        'safety_score': 88,
        'recommendation': 'good',
        'why_better': 'No artificial additives or preservatives',
        'availability': 'specialty',
      },
    ];
  }
}); 
