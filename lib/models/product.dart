import 'ingredient.dart';
import 'alternative.dart';

class Product {
  final String id;
  final String name;
  final String? brand;
  final String barcode;
  final String? imageUrl;
  final int? safetyScore;
  final String? safetyRating;
  final List<Ingredient> ingredients;
  final List<Alternative> alternatives;
  final DateTime? scannedAt;
  final List<Ingredient>? concerningIngredients;
  final int? concernsCount;

  // Additional JSON fields from database
  final Map<String, dynamic>? ingredientsJson;
  final Map<String, dynamic>? safetyCriteriaJson;
  final Map<String, dynamic>? matchStatsJson;
  final Map<String, dynamic>? nutritionConcernsJson;
  final String? userId;
  final String? description;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Product({
    required this.id,
    required this.name,
    this.brand,
    required this.barcode,
    this.imageUrl,
    this.safetyScore,
    this.safetyRating,
    this.ingredients = const [],
    this.alternatives = const [],
    this.scannedAt,
    this.concerningIngredients,
    this.concernsCount,
    this.ingredientsJson,
    this.safetyCriteriaJson,
    this.matchStatsJson,
    this.nutritionConcernsJson,
    this.userId,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  // Factory for database JSON (product_analysis_fullscan table)
  factory Product.fromDatabaseJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      name: json['name'] ?? 'Unknown Product',
      brand: json['brand'],
      barcode: json['barcode'] ?? '',
      imageUrl: json['image'],
      safetyScore: json['safety_score'],
      safetyRating: json['safety_rating'],
      scannedAt: json['scanned_at'] != null ? DateTime.parse(json['scanned_at']) : null,
      concernsCount: json['concerns_count'],
      ingredients: _parseIngredients(json['ingredients_json']),
      alternatives: _parseAlternatives(json['alternatives_json']),
      ingredientsJson: json['ingredients_json'],
      safetyCriteriaJson: json['safety_criteria_json'],
      matchStatsJson: json['match_stats_json'],
      nutritionConcernsJson: json['nutrition_concerns'],
      userId: json['user_id'],
      description: json['description'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  // Factory for API JSON (external sources)
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      name: json['name'] ?? json['product_name'] ?? 'Unknown Product',
      brand: json['brand'] ?? json['brands'],
      barcode: json['barcode'] ?? json['code'],
      imageUrl: json['image'] ?? json['image_url'],
      safetyScore: json['safety_score'],
      safetyRating: json['safety_rating'],
      ingredients: _parseIngredients(json['ingredients']),
      alternatives: _parseAlternatives(json['alternatives']),
      ingredientsJson: json['ingredients_json'],
      safetyCriteriaJson: json['safety_criteria_json'],
      matchStatsJson: json['match_stats_json'],
      nutritionConcernsJson: json['nutrition_concerns'],
      userId: json['user_id'],
      description: json['description'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  static List<Ingredient> _parseIngredients(dynamic ingredientsData) {
    if (ingredientsData == null) return [];
    
    if (ingredientsData is List) {
      return ingredientsData
          .map((item) => Ingredient.fromJson(item is Map<String, dynamic> ? item : {'name': item.toString()}))
          .toList();
    }
    
    return [];
  }

  static List<Alternative> _parseAlternatives(dynamic alternativesData) {
    if (alternativesData == null) return [];
    
    if (alternativesData is List) {
      return alternativesData
          .map((item) => Alternative.fromJson(item is Map<String, dynamic> ? item : {'name': item.toString()}))
          .toList();
    }
    
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'barcode': barcode,
      'image': imageUrl,
      'safety_score': safetyScore,
      'safety_rating': safetyRating,
      'scanned_at': scannedAt?.toIso8601String(),
      'concerns_count': concernsCount,
      'ingredients': ingredients.map((i) => i.toJson()).toList(),
      'alternatives': alternatives.map((a) => a.toJson()).toList(),
    };
  }

  Product copyWith({
    String? id,
    String? name,
    String? brand,
    String? barcode,
    String? imageUrl,
    int? safetyScore,
    String? safetyRating,
    List<Ingredient>? ingredients,
    List<Alternative>? alternatives,
    DateTime? scannedAt,
    List<Ingredient>? concerningIngredients,
    int? concernsCount,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      barcode: barcode ?? this.barcode,
      imageUrl: imageUrl ?? this.imageUrl,
      safetyScore: safetyScore ?? this.safetyScore,
      safetyRating: safetyRating ?? this.safetyRating,
      ingredients: ingredients ?? this.ingredients,
      alternatives: alternatives ?? this.alternatives,
      scannedAt: scannedAt ?? this.scannedAt,
      concerningIngredients: concerningIngredients ?? this.concerningIngredients,
      concernsCount: concernsCount ?? this.concernsCount,
    );
  }

  // Helper methods for safety rating
  bool get isSafe => safetyScore >= 80;
  bool get isCaution => safetyScore >= 50 && safetyScore < 80;
  bool get isUnsafe => safetyScore < 50;

  // Get safety color based on score
  String get safetyColor {
    if (isSafe) return 'green';
    if (isCaution) return 'yellow';
    return 'red';
  }
} 
